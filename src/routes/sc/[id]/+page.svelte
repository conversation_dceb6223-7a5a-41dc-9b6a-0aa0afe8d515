<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { scale } from 'svelte/transition';
  import type { PageData } from './$types';
  import type { SchoolUser } from '$lib/types';

  export let data: PageData;

  let showStudentSearch = false;
  let searchQuery = '';
  let searchResults: SchoolUser[] = [];
  let searching = false;
  let animateCards = false;

  onMount(async () => {
    animateCards = true;
  });

  function selectStudent(student: SchoolUser) {
    showStudentSearch = false;
    goto(`/s/a?student=${student.id}`);
  }

  function navigateToSignIn() {
    goto(`/sc/${data.school.id}/p/i`);
  }

  function navigateToSignOut() {
    goto(`/sc/${data.school.id}/p/o`);
  }
</script>


{#if !data.school}
  <div class="flex justify-center items-center min-h-[50vh]">
    <p class="text-destructive">School not found</p>
  </div>
{:else}
  <div class="container-soft">
    <h1 class="text-4xl font-bold text-foreground mb-8 text-center">
      {data.school.n}
    </h1>

    <!-- School Info Cards -->
    <div class="grid md:grid-cols-2 gap-6 mb-8">
      {#if animateCards}
        <div
          class="card-soft p-6"
          in:scale={{ delay: 100, duration: 400 }}
        >
          <h3 class="text-sm font-medium text-muted-foreground mb-2">Academic Years</h3>
          <p class="text-2xl font-semibold text-foreground">
            {data.school?.c ? Object.keys(data.school.c).filter(k => k !== 'n').length : 0} configured
          </p>
        </div>

        {#if data.schoolUser}
          <div
            class="card-soft p-6"
            in:scale={{ delay: 150, duration: 400 }}
          >
            <h3 class="text-sm font-medium text-muted-foreground mb-2">Your Role</h3>
            <p class="text-2xl font-semibold text-foreground">{data.schoolUser?.r || 'Unknown'}</p>
          </div>
        {/if}
      {/if}
    </div>

    <!-- Attendance Section -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold text-foreground mb-6">Attendance</h2>
      <div class="grid md:grid-cols-2 gap-6">
        {#if animateCards}
          <!-- Sign In Card -->
          <div
            class="card-soft p-8 text-center group transition-all duration-300 hover:shadow-soft-lg cursor-pointer"
            in:scale={{ delay: 200, duration: 400 }}
            on:click={navigateToSignIn}
            on:keydown={(e) => e.key === 'Enter' && navigateToSignIn()}
            role="button"
            tabindex="0"
          >
            <div class="flex flex-col items-center space-y-4">
              <div class="w-16 h-16 bg-accent rounded-lg flex items-center justify-center text-accent-foreground group-hover:scale-110 transition-transform">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-foreground">Sign Users In</h3>
              <p class="text-muted-foreground">Scan QR codes to record arrivals</p>
            </div>
          </div>

          <!-- Sign Out Card -->
          <div
            class="card-soft p-8 text-center group transition-all duration-300 hover:shadow-soft-lg cursor-pointer"
            in:scale={{ delay: 250, duration: 400 }}
            on:click={navigateToSignOut}
            on:keydown={(e) => e.key === 'Enter' && navigateToSignOut()}
            role="button"
            tabindex="0"
          >
            <div class="flex flex-col items-center space-y-4">
              <div class="w-16 h-16 bg-secondary rounded-lg flex items-center justify-center text-secondary-foreground group-hover:scale-110 transition-transform">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4M16 17l5-5-5-5M21 12H9"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-foreground">Sign Users Out</h3>
              <p class="text-muted-foreground">Scan QR codes to record departures</p>
            </div>
          </div>
        {/if}
      </div>
    </div>

    <!-- Admin Actions -->
    {#if data.isAdmin}
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-foreground mb-6">Admin Actions</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          {#if animateCards}
            

            <a
              href={`/sc/${data.school.id}/as`}
              class="btn-soft-secondary flex items-center justify-center gap-2 p-4 text-center"
              in:scale={{ delay: 350, duration: 400 }}
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Add Student
            </a>

            <a
              href={`/sc/${data.school.id}/s`}
              class="btn-soft-secondary flex items-center justify-center gap-2 p-4 text-center"
              in:scale={{ delay: 400, duration: 400 }}
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              View Students
            </a>

            <a
              href="/sc/r"
              class="btn-soft-secondary flex items-center justify-center gap-2 p-4 text-center"
              in:scale={{ delay: 450, duration: 400 }}
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              Join Requests
            </a>
          {/if}
        </div>
      </div>
    {/if}

  <!-- Student Search Modal -->
  {#if showStudentSearch}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="card-soft max-w-md w-full mx-4 p-6">
        <h3 class="text-xl font-semibold text-foreground mb-4">Search Student</h3>

        <form on:submit|preventDefault={async () => {
          if (!searchQuery.trim()) return;

          searching = true;
          try {
            // In a real implementation, this would call a server action
            // For now, we'll simulate search
            await new Promise(resolve => setTimeout(resolve, 500));
            searchResults = []; // Would be populated from server
          } finally {
            searching = false;
          }
        }} class="space-y-4">
          <div class="space-y-2">
            <label for="search" class="text-sm font-medium text-muted-foreground">Student Name</label>
            <input
              id="search"
              name="search"
              type="text"
              bind:value={searchQuery}
              placeholder="Enter student name"
              disabled={searching}
              class="input-soft"
            />
          </div>

          {#if searchResults.length > 0}
            <div class="space-y-2">
              {#each searchResults as student}
                <button
                  type="button"
                  class="btn-soft-ghost w-full text-left justify-start p-3"
                  on:click={() => selectStudent(student)}
                >
                  <div class="flex flex-col">
                    <p class="font-medium">{student.n}</p>
                    <p class="text-sm text-muted-foreground">Role: {Array.isArray(student.r) ? student.r.join(', ') : student.r}</p>
                  </div>
                </button>
              {/each}
            </div>
          {/if}

          <div class="flex gap-2">
            <button
              type="submit"
              class="btn-soft flex-1"
              disabled={searching || !searchQuery.trim()}
            >
              {searching ? 'Searching...' : 'Search'}
            </button>
            <button
              type="button"
              class="btn-soft-secondary"
              on:click={() => showStudentSearch = false}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  {/if}
  </div>
{/if}