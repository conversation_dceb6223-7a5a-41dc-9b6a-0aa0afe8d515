<script lang="ts">
  import { onMount } from 'svelte';
  import QRScanner from '$lib/components/QRScanner.svelte';
  import { toast } from '$lib/stores/toast';
  let isScanning = false;
  let lastScannedData = '';
  let cameraSupported = false;

  onMount(async () => {
    // Check camera support
    cameraSupported = !!(navigator?.mediaDevices?.getUserMedia);
  });

  function startScanning() {
    isScanning = true;
    lastScannedData = '';
  }

  function stopScanning() {
    isScanning = false;
  }

  function handleScan(event: CustomEvent<{ data: string }>) {
    const qrData = event.detail.data;
    console.log('QR Code scanned:', qrData);
    lastScannedData = qrData;
    toast.success(`QR Code scanned: ${qrData}`);
    stopScanning();
  }

  function handleScanError(event: CustomEvent<{ message: string }>) {
    const errorMessage = event.detail.message;
    console.error('QR Scanner error:', errorMessage);
    toast.error(errorMessage);
    stopScanning();
  }
</script>

<svelte:head>
  <title>QR Scanner Test</title>
</svelte:head>

<div class="container mx-auto p-6">
  <div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">QR Scanner Test</h1>
    
    <div class="space-y-6">
      <!-- Scanner Controls -->
      <div class="card-soft p-6">
        <h2 class="text-xl font-semibold mb-4">Scanner Controls</h2>
        <div class="flex gap-4">
          <button 
            class="btn-primary"
            on:click={startScanning}
            disabled={isScanning}
          >
            {isScanning ? 'Scanning...' : 'Start Scanning'}
          </button>
          
          <button 
            class="btn-secondary"
            on:click={stopScanning}
            disabled={!isScanning}
          >
            Stop Scanning
          </button>
        </div>
      </div>

      <!-- Scanner Component -->
      <div class="card-soft p-6">
        <h2 class="text-xl font-semibold mb-4">Scanner</h2>
        <QRScanner
          isActive={isScanning}
          facingMode="environment"
          on:scan={handleScan}
          on:error={handleScanError}
        />
      </div>

      <!-- Test Instructions -->
      <div class="card-soft p-6">
        <h2 class="text-xl font-semibold mb-4">Test Instructions</h2>
        <div class="space-y-2 text-sm">
          <p>1. Click "Start Scanning" to activate the camera</p>
          <p>2. Point your camera at any QR code</p>
          <p>3. The scanner should detect and display the QR code data</p>
          <p>4. Check the browser console for debug information</p>
        </div>
      </div>

      <!-- Results -->
      <div class="card-soft p-6">
        <h2 class="text-xl font-semibold mb-4">Last Scanned Data</h2>
        {#if lastScannedData}
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <p class="text-green-800 font-mono break-all">{lastScannedData}</p>
          </div>
        {:else}
          <p class="text-gray-500">No QR code scanned yet</p>
        {/if}
      </div>

      <!-- Debug Info -->
      <div class="card-soft p-6">
        <h2 class="text-xl font-semibold mb-4">Debug Info</h2>
        <div class="space-y-2 text-sm">
          <p><strong>Scanner Active:</strong> {isScanning}</p>
          <p><strong>Camera Support:</strong> {cameraSupported ? 'Yes' : 'No'}</p>
          <p><strong>HTTPS:</strong> {location.protocol === 'https:' ? 'Yes' : 'No'}</p>
          <p><strong>User Agent:</strong> {navigator.userAgent}</p>
        </div>
      </div>
    </div>
  </div>
</div>
