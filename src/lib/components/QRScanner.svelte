<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import jsQR from 'jsqr';

  export let isActive = false;
  export let facingMode: 'user' | 'environment' = 'environment';

  // Event callbacks
  interface $$Events {
    scan: CustomEvent<{ data: string }>;
    error: CustomEvent<{ message: string }>;
  }

  let video: HTMLVideoElement;
  let canvas: HTMLCanvasElement;
  let containerElement: HTMLDivElement;
  let stream: MediaStream | null = null;
  let scanning = false;
  let animationFrame: number;
  let videoReady = false;

  // Create event dispatcher function
  function dispatchEvent(type: string, detail: any) {
    if (containerElement) {
      containerElement.dispatchEvent(new CustomEvent(type, { detail }));
    }
  }

  onMount(() => {
    if (isActive) {
      startScanning();
    }
  });

  onDestroy(() => {
    stopScanning();
  });

  $: if (isActive) {
    startScanning();
  } else {
    stopScanning();
  }

  async function startScanning() {
    if (scanning) return;

    try {
      scanning = true;
      videoReady = false;

      // Request camera access
      stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (video) {
        video.srcObject = stream;

        // Wait for video to be ready
        video.addEventListener('loadedmetadata', () => {
          videoReady = true;
          // Start scanning after a short delay to ensure video is fully loaded
          setTimeout(() => {
            if (scanning && videoReady) {
              scanFrame();
            }
          }, 100);
        });

        await video.play();
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      dispatchEvent('error', { message: 'Failed to access camera. Please ensure camera permissions are granted.' });
      scanning = false;
    }
  }

  function stopScanning() {
    scanning = false;
    
    if (animationFrame) {
      cancelAnimationFrame(animationFrame);
    }
    
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      stream = null;
    }
  }

  function scanFrame() {
    if (!scanning || !video || !canvas || !videoReady) return;

    const context = canvas.getContext('2d');
    if (!context) return;

    // Check if video has valid dimensions
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      // Video not ready yet, try again
      animationFrame = requestAnimationFrame(scanFrame);
      return;
    }

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get image data for QR code detection
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

    try {
      const qrData = detectQRCode(imageData);
      if (qrData) {
        console.log('QR Code detected:', qrData);
        dispatchEvent('scan', { data: qrData });
        stopScanning(); // Stop scanning after successful detection
        return;
      }
    } catch (error) {
      console.error('QR detection error in scanFrame:', error);
    }

    // Continue scanning
    animationFrame = requestAnimationFrame(scanFrame);
  }

  // QR code detection using jsQR
  function detectQRCode(imageData: ImageData): string | null {
    try {
      // Try different inversion attempts for better detection
      const code = jsQR(imageData.data, imageData.width, imageData.height, {
        inversionAttempts: 'attemptBoth',
      });

      if (code) {
        console.log('QR Code found:', code.data);
        return code.data;
      }
      return null;
    } catch (error) {
      console.error('QR detection error:', error);
      return null;
    }
  }


</script>

<div
  class="qr-scanner"
  bind:this={containerElement}
>
  {#if isActive}
    <div class="scanner-container">
      <video
        bind:this={video}
        class="scanner-video"
        autoplay
        muted
        playsinline
      ></video>

      <canvas
        bind:this={canvas}
        class="scanner-canvas"
        style="display: none;"
      ></canvas>

      <!-- Scanning overlay -->
      <div class="scanner-overlay">
        <div class="scan-area">
          <div class="scan-corners">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
          </div>
        </div>

        <div class="scanner-instructions">
          <p>Position QR code within the frame</p>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .qr-scanner {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .scanner-container {
    position: relative;
    width: 100%;
    height: 400px;
    background: var(--foreground);
    border-radius: var(--radius-card);
    overflow: hidden;
    box-shadow: var(--shadow-soft-lg);
  }

  .scanner-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);
  }

  .scan-area {
    width: 250px;
    height: 250px;
    position: relative;
  }

  .scan-corners {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid var(--primary);
    border-radius: var(--radius);
  }

  .corner.top-left {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
  }

  .corner.top-right {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
  }

  .corner.bottom-left {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
  }

  .corner.bottom-right {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
  }

  .scanner-instructions {
    margin-top: var(--space-xl);
    text-align: center;
    color: white;
  }

  .scanner-instructions p {
    margin-bottom: var(--space-md);
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }


</style>
